import { app } from './app.js';
import { logger } from './logger.js';
import { stopWatchdog } from './utils/watchdog/index.js';
import { resourceMonitor } from './utils/resource-monitor.js';
import { cleanupRedisAdapter } from './utils/socketio-redis.js';
import { stopSocketIOMonitoring } from './utils/socketio-monitor.js';

// Use config values - DigitalOcean will override via environment variables
const port = app.get('port');
const host = app.get('host');

let server: any;
let isShuttingDown = false;

// Enhanced error handling
process.on('unhandledRejection', (reason, p) => {
	logger.error('Unhandled Rejection at: Promise ', p, reason);
	// Don't exit immediately, let watchdog handle it
});

process.on('uncaughtException', (error) => {
	logger.error('Uncaught Exception:', error);
	// Log the error but don't exit immediately
	// Let the watchdog detect the issue and handle graceful shutdown
});

// Graceful shutdown handling
const gracefulShutdown = async (signal: string) => {
	if (isShuttingDown) {
		logger.warn(`${signal} received again, forcing exit`);
		process.exit(1);
	}

	isShuttingDown = true;
	logger.info(`${signal} received, starting graceful shutdown...`);

	try {
		// Stop accepting new connections
		if (server) {
			await new Promise<void>((resolve, reject) => {
				server.close((err: any) => {
					if (err) reject(err);
					else resolve();
				});
			});
			logger.info('HTTP server closed');
		}

		// Stop Socket.IO monitoring and Redis connections
		stopSocketIOMonitoring();
		await cleanupRedisAdapter(app);

		// Stop watchdog and clean up resources
		stopWatchdog();
		resourceMonitor.cleanup();

		// Close MongoDB connections
		try {
			const mongoDb = await app.get('mongodbClient') as any;
			if (mongoDb && mongoDb.client) {
				await mongoDb.client.close();
				logger.info('MongoDB connection closed');
			}
		} catch (error) {
			logger.error('Error closing MongoDB connection:', error);
		}

		// Give remaining operations time to complete
		await new Promise(resolve => setTimeout(resolve, 5000));

		logger.info('Graceful shutdown completed');
		process.exit(0);
	} catch (error) {
		logger.error('Error during graceful shutdown:', error);
		process.exit(1);
	}
};

// Handle various shutdown signals
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));
process.on('SIGUSR2', () => gracefulShutdown('SIGUSR2')); // nodemon restart

// Validate port and host before starting
if (!port) {
	console.error('[STARTUP ERROR] No port configured!');
	process.exit(1);
}

if (!host) {
	console.error('[STARTUP ERROR] No host configured!');
	process.exit(1);
}

console.log(`[BOOT] will bind host=${host} port=${port}`);

server = app.listen(port, host).then((httpServer: any) => {
	server = httpServer;
	console.log(`Feathers app listening on http://${host}:${port}`);
	logger.info(`Feathers app listening on http://${host}:${port}`);
	return httpServer;
}).catch((error: any) => {
	console.error('Failed to start server:', error);
	logger.error('Failed to start server:', error);
	process.exit(1);
});
