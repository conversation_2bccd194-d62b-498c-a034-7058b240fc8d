import { app } from './app.js';
import { logger } from './logger.js';

const port = app.get('port');
const host = app.get('host');

console.log('got port and host', port, host);

process.on('unhandledRejection', (reason, p) =>
	logger.error('Unhandled Rejection at: Promise ', p, reason)
);

console.log(`About to listen on ${host}:${port}`)

await app.listen(port, host).then(() => {
	logger.info(`Feathers app listening on ${host}:${port}`);
});
