// Redis adapter configuration for Socket.IO clustering
import { createAdapter } from '@socket.io/redis-adapter';
import { createClient } from 'redis';
import type { Application } from '../declarations.js';

export const configureRedisAdapter = async (app: Application, io: any) => {
    const redisUrl = app.get('sessions').valkey.url;

    if (!redisUrl) {
        console.log('[Socket.IO] No Redis/Valkey URL provided, running in single-server mode');
        return;
    }

    console.log('[Socket.IO] Redis/Valkey URL found, attempting connection...');

    try {
        console.log('[Socket.IO] Configuring Redis/Valkey adapter...');

        // Add timeout to prevent hanging
        const connectionTimeout = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('Redis connection timeout after 10 seconds')), 10000);
        });

        // Create Redis clients for pub/sub with shorter timeouts
        const pubClient = createClient({
            url: redisUrl,
            socket: {
                connectTimeout: 3000, // Reduced from 5000
                reconnectStrategy: (retries) => Math.min(retries * 50, 500)
            }
        });

        const subClient = pubClient.duplicate();

        // Handle Redis connection events
        pubClient.on('error', (err) => {
            console.error('[Socket.IO Redis Pub] Connection error:', err);
        });

        subClient.on('error', (err) => {
            console.error('[Socket.IO Redis Sub] Connection error:', err);
        });

        pubClient.on('connect', () => {
            console.log('[Socket.IO Redis Pub] Connected');
        });

        subClient.on('connect', () => {
            console.log('[Socket.IO Redis Sub] Connected');
        });

        // Connect to Redis with timeout protection
        console.log('[Socket.IO] Attempting Redis connection...');
        const connectPromise = Promise.all([
            pubClient.connect(),
            subClient.connect()
        ]);

        await Promise.race([connectPromise, connectionTimeout]);
        console.log('[Socket.IO] Redis clients connected successfully');

        // Configure Socket.IO adapter
        io.adapter(createAdapter(pubClient, subClient, {
            key: 'socket.io',
            requestsTimeout: 5000,
        }));

        console.log('[Socket.IO] Redis adapter configured successfully');

        // Store clients for cleanup
        app.set('redisClients', { pubClient, subClient });

    } catch (error) {
        console.error('[Socket.IO] Failed to configure Redis adapter:', error);
        console.error('[Socket.IO] Error details:', error instanceof Error ? error.message : String(error));
        console.log('[Socket.IO] Falling back to single-server mode');

        // Make sure to clean up any partial connections
        try {
            const clients = app.get('redisClients') as any;
            if (clients && clients.pubClient && clients.subClient) {
                await Promise.allSettled([
                    clients.pubClient.quit(),
                    clients.subClient.quit()
                ]);
            }
        } catch (cleanupError) {
            console.error('[Socket.IO] Error during Redis cleanup:', cleanupError);
        }
    }
};

export const cleanupRedisAdapter = async (app: Application) => {
    const clients = app.get('redisClients') as any;
    if (clients) {
        try {
            await Promise.all([
                clients.pubClient.quit(),
                clients.subClient.quit()
            ]);
            console.log('[Socket.IO] Redis clients disconnected');
        } catch (error) {
            console.error('[Socket.IO] Error disconnecting Redis clients:', error);
        }
    }
};
