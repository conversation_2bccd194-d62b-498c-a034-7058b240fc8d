{"port": "PORT", "host": "HOSTNAME", "env": "ENV", "root_url": "ROOT_URL", "mailer": {"key": "SENDGRID_API_KEY"}, "sessions": {"valkey": {"url": "VALKEY_URL"}}, "sms": {"key": "TWILIO_API_KEY", "id": "TWILIO_SID", "from": "FROM_PN"}, "openai": {"key": "OPEN_AI_KEY", "org": "OPEN_AI_ORG"}, "x": {"key": "X_AI_KEY"}, "video": {"bucket": "BUNNY_BUCKET", "key": "BUNNY_KEY", "collection": "BUNNY_COLLECTION"}, "banking": {"stripe": {"secret_key": "STRIPE_TEST_SK", "public_key": "STRIPE_TEST_PK", "issuing_authorization_secret": "STRIPE_ISSUING_AUTH_SECRET"}, "moov": {"wallet_webhook_secret": "MOOV_WEBHOOK_SECRET", "public_key": "MOOV_PK", "secret_key": "MOOV_SK", "account_id": "MOOV_ID", "public_key_dev": "MOOV_PK_DEV", "secret_key_dev": "MOOV_SK_DEV", "account_id_dev": "MOOV_ID_DEV"}, "ramp": {"client_id": "RAMP_CLIENT_ID", "secret": "RAMP_SECRET"}}, "s3": {"accessKeyId": "STORJ_ACCESS_KEY", "secretAccessKey": "STORJ_SECRET_KEY", "bucket": "STORJ_BUCKET", "region": "STORJ_REGION", "endpoint": "STORJ_ENDPOINT"}, "google": {"api_key": "GOOGLE_API_KEY"}, "encryption": {"field_key": "FIELD_ENCRYPTOR", "transport_key": "TRANSPORT_KEY", "pin_key": "PIN_KEY"}, "cms": {"marketplace_key": "MARKETPLACE_KEY", "finder_key": "FINDER_KEY"}, "authentication": {"webauthn": {"expectedOrigin": "WEBAUTHN_EXPECTED_ORIGIN", "allowedRpIds": "WEBAUTHN_ALLOWED_RPIDS"}, "secret": "UCAN_SK", "api_config": {"secret": "API_KEY_SECRET", "allowed_keys": {"twilio": "TWILIO_API_ALLOWED"}}, "origin": "ROOT_URL", "oauth": {"defaults": {"origin": "ROOT_URL"}, "redirect": "REDIRECT_URL", "google": {"key": "OAUTH_GOOGLE_KEY", "secret": "OAUTH_GOOGLE_SECRET"}, "facebook": {"key": "OAUTH_FACEBOOK_KEY", "secret": "OAUTH_FACEBOOK_SECRET"}, "linkedin": {"key": "OAUTH_LINKEDIN_KEY", "secret": "OAUTH_LINKEDIN_SECRET"}, "github": {"key": "OAUTH_GITHUB_KEY", "secret": "OAUTH_GITHUB_SECRET"}, "twitter": {"key": "OAUTH_TWITTER_KEY", "secret": "OAUTH_TWITTER_SECRET"}, "apple": {"key": "OAUTH_APPLE_KEY", "secret": "OAUTH_APPLE_SECRET"}}}, "web3": {"key": "WEB3_KEY"}, "google-cloud": {"projectId": "GOOGLE_CLOUD_PROJECT_ID", "bucket": "symbol_general"}, "mongodb": "MONGO_DB_URI", "salesTax": {"api_key": "ZIP_TAX"}, "turnstile": {"turnstile_sk": "CF_TURNSTILE_KEY"}}